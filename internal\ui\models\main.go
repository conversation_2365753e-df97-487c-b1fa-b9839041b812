package models

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/config"
	"arien-ai/internal/executor"
	"arien-ai/internal/llm"
	"arien-ai/internal/session"
	"arien-ai/internal/ui/components"
	"arien-ai/internal/ui/styles"
	"arien-ai/internal/utils"
	"arien-ai/pkg/types"
)

// InteractiveOptions contains options for the interactive mode
type InteractiveOptions struct {
	SessionID        string
	ProviderOverride string
	ModelOverride    string
	SafeMode         bool
	NoHistory        bool
	DebugMode        bool
}

// ExitInfo contains information about the exit state
type ExitInfo struct {
	Message           string
	ShouldSaveSession bool
	HasErrors         bool
	SessionStats      *SessionStats
}

// SessionStats contains statistics about the session
type SessionStats struct {
	MessageCount int
	CommandCount int
	Duration     time.Duration
	TokenUsage   types.TokenUsage
}

// InputMode represents different input modes
type InputMode int

const (
	ModeNormal InputMode = iota
	ModeCommand
	ModeSearch
)

// SlashCommand represents a slash command
type SlashCommand struct {
	Name        string
	Description string
	Handler     func(*MainModel, []string) tea.Cmd
}

// AppMsg represents application-specific messages
type AppMsg interface {
	AppMsg()
}

// ThinkingStartMsg starts the thinking animation
type ThinkingStartMsg struct {
	Message string
}

func (ThinkingStartMsg) AppMsg() {}

// ThinkingStopMsg stops the thinking animation
type ThinkingStopMsg struct{}

func (ThinkingStopMsg) AppMsg() {}

// LLMResponseMsg contains an LLM response
type LLMResponseMsg struct {
	Message *types.Message
	Error   error
}

func (LLMResponseMsg) AppMsg() {}



// ToolExecutionCompleteMsg contains tool execution results
type ToolExecutionCompleteMsg struct {
	Results []types.ToolResult
}

func (ToolExecutionCompleteMsg) AppMsg() {}



// MainModel represents the main terminal interface model
type MainModel struct {
	configManager *config.Manager
	sessionManager *session.Manager
	llmClient     llm.Client
	executor      *executor.ShellExecutor
	theme         *styles.Theme
	options       InteractiveOptions

	// Current session
	session *types.Session

	// UI state
	width  int
	height int

	// UI Components
	header    *components.HeaderComponent
	history   *components.HistoryComponent
	input     *components.InputComponent
	thinking  *components.ThinkingComponent
	statusBar *components.StatusBarComponent

	// Application state
	started      time.Time
	exitInfo     ExitInfo
	currentMode  InputMode
	isThinking   bool
	lastError    error

	// Slash commands state
	showSlashMenu bool
	slashCommands []SlashCommand
	selectedSlash int
}

// NewMainModel creates a new main terminal model
func NewMainModel(configManager *config.Manager, options InteractiveOptions) (*MainModel, error) {
	config := configManager.Get()

	// Create session manager
	sessionManager, err := session.NewManager(
		configManager.GetSessionDirectory(),
		config.Session.MaxSessions,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create session manager: %w", err)
	}

	// Create LLM client
	llmClient, err := llm.NewClient(config.LLM)
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}

	// Create shell executor
	shellExecutor := executor.NewShellExecutor(config.Security)

	// Create or load session
	var currentSession *types.Session
	if options.SessionID != "" {
		currentSession, err = sessionManager.LoadSession(options.SessionID)
		if err != nil {
			return nil, fmt.Errorf("failed to load session: %w", err)
		}
	} else {
		currentSession = types.NewSession("Interactive Session", "CLI Terminal Session", config.LLM)
	}

	// Create theme
	theme := styles.GetTheme(config.UI.Theme)

	// Create UI components
	header := components.NewHeaderComponent(theme, currentSession, config)
	history := components.NewHistoryComponent(theme)
	input := components.NewInputComponent(theme)
	thinking := components.NewThinkingComponent(theme)
	statusBar := components.NewStatusBarComponent(theme, currentSession, config)

	// Set initial messages in history
	if len(currentSession.Messages) > 0 {
		history.SetMessages(currentSession.Messages)
	}

	// Create slash commands
	slashCommands := createSlashCommands()

	return &MainModel{
		configManager:  configManager,
		sessionManager: sessionManager,
		llmClient:      llmClient,
		executor:       shellExecutor,
		theme:          theme,
		options:        options,
		session:        currentSession,
		header:         header,
		history:        history,
		input:          input,
		thinking:       thinking,
		statusBar:      statusBar,
		started:        time.Now(),
		currentMode:    ModeNormal,
		slashCommands:  slashCommands,
	}, nil
}

// Init initializes the main model
func (m *MainModel) Init() tea.Cmd {
	m.statusBar.SetMessage("Ready")
	return m.input.Focus()
}

// Update handles messages and updates the model
func (m *MainModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.updateComponentSizes()
		return m, nil

	case tea.KeyMsg:
		return m.handleKeyMsg(msg)

	case ThinkingStartMsg:
		m.isThinking = true
		return m, m.thinking.Start(msg.Message)

	case ThinkingStopMsg:
		m.isThinking = false
		m.thinking.Stop()
		return m, nil

	case LLMResponseMsg:
		return m.handleLLMResponse(msg)

	case CommandExecutedMsg:
		return m.handleCommandExecuted(msg)

	case ToolExecutionCompleteMsg:
		return m.handleToolExecutionComplete(msg)
	}

	// Update components (input is handled in handleKeyMsg)
	var cmd tea.Cmd

	cmd = m.history.Update(msg)
	cmds = append(cmds, cmd)

	cmd = m.thinking.Update(msg)
	cmds = append(cmds, cmd)

	return m, tea.Batch(cmds...)
}

// View renders the main terminal interface
func (m *MainModel) View() string {
	if m.width == 0 {
		return "Loading..."
	}

	// Calculate component heights
	headerHeight := m.header.GetHeight()
	statusHeight := m.statusBar.GetHeight()
	inputHeight := m.input.GetHeight()

	// Calculate available height for history
	historyHeight := m.height - headerHeight - statusHeight - inputHeight - 2 // 2 for margins
	if historyHeight < 5 {
		historyHeight = 5
	}

	// Update component sizes
	m.history.SetSize(m.width, historyHeight)

	// Render components
	header := m.header.Render()
	history := m.history.Render()
	input := m.input.Render()
	status := m.statusBar.Render()

	// Add thinking animation if active
	if m.isThinking && m.thinking.IsActive() {
		thinking := m.thinking.Render()
		history += "\n" + thinking
	}

	// Add slash menu if active
	if m.showSlashMenu {
		slashMenu := m.renderSlashMenu()
		input += "\n" + slashMenu
	}

	// Combine all components
	return lipgloss.JoinVertical(
		lipgloss.Left,
		header,
		history,
		input,
		status,
	)
}

// GetExitInfo returns information about the exit state
func (m *MainModel) GetExitInfo() ExitInfo {
	return m.exitInfo
}

// SaveCurrentSession saves the current session
func (m *MainModel) SaveCurrentSession() error {
	if m.sessionManager == nil || m.session == nil {
		return fmt.Errorf("session manager or session is nil")
	}
	return m.sessionManager.SaveSession(m.session)
}

// Placeholder implementations for missing methods


func (m *MainModel) handleCommandExecuted(msg CommandExecutedMsg) (tea.Model, tea.Cmd) {
	// Add command result to history
	var content string
	if msg.Error != nil {
		content = fmt.Sprintf("Command '%s' failed: %v", msg.Command, msg.Error)
	} else {
		content = fmt.Sprintf("Command '%s' executed successfully:\n%s", msg.Command, msg.Result)
	}

	resultMsg := types.NewMessage(types.RoleSystem, types.MessageTypeSystem, content)
	m.session.AddMessage(resultMsg)
	m.history.AddMessage(resultMsg)

	return m, nil
}

func (m *MainModel) handleToolExecutionComplete(msg ToolExecutionCompleteMsg) (tea.Model, tea.Cmd) {
	// Process tool execution results
	for _, result := range msg.Results {
		if result.Success {
			m.statusBar.SetMessage(fmt.Sprintf("Tool %s executed successfully", result.ToolCallID))
		} else {
			m.statusBar.SetMessage(fmt.Sprintf("Tool %s failed: %s", result.ToolCallID, result.Error))
		}
	}

	// Continue conversation with LLM if needed
	// The LLM can see the tool results and respond accordingly
	return m, nil
}

func (m *MainModel) executeToolCalls(toolCalls []types.ToolCall) tea.Cmd {
	return func() tea.Msg {
		var results []types.ToolResult

		for _, toolCall := range toolCalls {
			result := m.executeToolCall(toolCall)
			results = append(results, result)

			// Add tool result message to session
			toolResultMsg := types.NewMessage(types.RoleTool, types.MessageTypeTool, result.Content)
			toolResultMsg.ToolResult = &result
			m.session.AddMessage(toolResultMsg)
			m.history.AddMessage(toolResultMsg)
		}

		return ToolExecutionCompleteMsg{Results: results}
	}
}

// executeToolCall executes a single tool call
func (m *MainModel) executeToolCall(toolCall types.ToolCall) types.ToolResult {
	result := types.ToolResult{
		ToolCallID: toolCall.ID,
		Success:    false,
		StartTime:  time.Now(),
	}

	switch toolCall.Function.Name {
	case "execute_shell_command":
		result = m.executeShellCommand(toolCall)
	case "read_file":
		result = m.executeReadFile(toolCall)
	case "write_file":
		result = m.executeWriteFile(toolCall)
	case "get_system_info":
		result = m.executeSystemInfo(toolCall)
	default:
		result.Error = fmt.Sprintf("Unknown tool: %s", toolCall.Function.Name)
		result.Content = fmt.Sprintf("Error: Tool '%s' is not supported", toolCall.Function.Name)
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	return result
}

// executeShellCommand executes a shell command tool call
func (m *MainModel) executeShellCommand(toolCall types.ToolCall) types.ToolResult {
	result := types.ToolResult{
		ToolCallID: toolCall.ID,
		Success:    false,
		StartTime:  time.Now(),
	}

	// Parse arguments from JSON
	var args map[string]interface{}
	if err := json.Unmarshal(toolCall.Function.Arguments, &args); err != nil {
		result.Error = fmt.Sprintf("Failed to parse arguments: %v", err)
		result.Content = "Error: Invalid arguments format"
		return result
	}

	command, ok := args["command"].(string)
	if !ok {
		result.Error = "Missing or invalid 'command' parameter"
		result.Content = "Error: Command parameter is required"
		return result
	}

	workingDir, _ := args["working_directory"].(string)
	if workingDir == "" {
		workingDir = "."
	}

	timeout := 30 * time.Second
	if timeoutVal, ok := args["timeout"].(float64); ok {
		timeout = time.Duration(timeoutVal) * time.Second
	}

	captureOutput := true
	if captureVal, ok := args["capture_output"].(bool); ok {
		captureOutput = captureVal
	}

	// Execute command
	execResult := m.executor.ExecuteCommand(command, workingDir, timeout, captureOutput)

	result.Success = execResult.Success
	result.Content = execResult.Output
	result.Error = execResult.Error
	result.ExitCode = execResult.ExitCode
	result.Duration = execResult.Duration

	return result
}

// executeReadFile executes a read file tool call
func (m *MainModel) executeReadFile(toolCall types.ToolCall) types.ToolResult {
	result := types.ToolResult{
		ToolCallID: toolCall.ID,
		Success:    false,
		StartTime:  time.Now(),
	}

	// Parse arguments from JSON
	var args map[string]interface{}
	if err := json.Unmarshal(toolCall.Function.Arguments, &args); err != nil {
		result.Error = fmt.Sprintf("Failed to parse arguments: %v", err)
		result.Content = "Error: Invalid arguments format"
		return result
	}

	filePath, ok := args["file_path"].(string)
	if !ok {
		result.Error = "Missing or invalid 'file_path' parameter"
		result.Content = "Error: File path parameter is required"
		return result
	}

	// Execute file read using executor
	execResult := m.executor.ExecuteFileRead(filePath)

	result.Success = execResult.Success
	result.Content = execResult.Output
	result.Error = execResult.Error
	result.Duration = execResult.Duration

	return result
}

// executeWriteFile executes a write file tool call
func (m *MainModel) executeWriteFile(toolCall types.ToolCall) types.ToolResult {
	result := types.ToolResult{
		ToolCallID: toolCall.ID,
		Success:    false,
		StartTime:  time.Now(),
	}

	// Parse arguments from JSON
	var args map[string]interface{}
	if err := json.Unmarshal(toolCall.Function.Arguments, &args); err != nil {
		result.Error = fmt.Sprintf("Failed to parse arguments: %v", err)
		result.Content = "Error: Invalid arguments format"
		return result
	}

	filePath, ok := args["file_path"].(string)
	if !ok {
		result.Error = "Missing or invalid 'file_path' parameter"
		result.Content = "Error: File path parameter is required"
		return result
	}

	content, ok := args["content"].(string)
	if !ok {
		result.Error = "Missing or invalid 'content' parameter"
		result.Content = "Error: Content parameter is required"
		return result
	}

	// Execute file write using executor
	execResult := m.executor.ExecuteFileWrite(filePath, content)

	result.Success = execResult.Success
	result.Content = execResult.Output
	result.Error = execResult.Error
	result.Duration = execResult.Duration

	return result
}

// executeSystemInfo executes a system info tool call
func (m *MainModel) executeSystemInfo(toolCall types.ToolCall) types.ToolResult {
	result := types.ToolResult{
		ToolCallID: toolCall.ID,
		Success:    false,
		StartTime:  time.Now(),
	}

	// Parse arguments from JSON
	var args map[string]interface{}
	if err := json.Unmarshal(toolCall.Function.Arguments, &args); err != nil {
		result.Error = fmt.Sprintf("Failed to parse arguments: %v", err)
		result.Content = "Error: Invalid arguments format"
		return result
	}

	infoType := "basic"
	if infoTypeVal, ok := args["info_type"].(string); ok {
		infoType = infoTypeVal
	}

	includeSensitive := false
	if sensitiveVal, ok := args["include_sensitive"].(bool); ok {
		includeSensitive = sensitiveVal
	}

	// Execute system info using executor
	execResult := m.executor.ExecuteSystemInfo(infoType, includeSensitive)

	result.Success = execResult.Success
	result.Content = execResult.Output
	result.Error = execResult.Error
	result.Duration = execResult.Duration

	return result
}



// updateComponentSizes updates the sizes of all UI components
func (m *MainModel) updateComponentSizes() {
	m.header.SetWidth(m.width)
	m.input.SetWidth(m.width)
	m.thinking.SetWidth(m.width)
	m.statusBar.SetWidth(m.width)
}

// handleKeyMsg handles keyboard input
func (m *MainModel) handleKeyMsg(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "ctrl+c":
		m.prepareExit()
		return m, tea.Quit

	case "enter":
		return m.handleEnterKey()

	case "/":
		if m.currentMode == ModeNormal && !m.input.HasText() {
			m.currentMode = ModeCommand
			m.showSlashMenu = true
			m.input.SetMode(components.ModeCommand)
			return m, nil
		}

	case "esc":
		if m.showSlashMenu {
			m.showSlashMenu = false
			m.currentMode = ModeNormal
			m.input.SetMode(components.ModeNormal)
			m.input.Clear()
			return m, nil
		}

	case "up", "down":
		if m.showSlashMenu {
			return m.handleSlashNavigation(msg.String())
		}
	}

	// Pass all other keys to the input component for normal text input
	cmd := m.input.Update(msg)
	return m, cmd
}

// handleEnterKey handles the enter key press
func (m *MainModel) handleEnterKey() (tea.Model, tea.Cmd) {
	inputValue := strings.TrimSpace(m.input.GetValue())
	if inputValue == "" {
		return m, nil
	}

	// Handle slash commands
	if m.currentMode == ModeCommand {
		return m.handleSlashCommand(inputValue)
	}

	// Handle normal message
	return m.handleUserMessage(inputValue)
}

// handleUserMessage processes a user message
func (m *MainModel) handleUserMessage(message string) (tea.Model, tea.Cmd) {
	// Clear input
	m.input.Clear()

	// Add user message to session
	userMsg := types.NewUserMessage(message)
	m.session.AddMessage(userMsg)
	m.history.AddMessage(userMsg)

	// Start thinking animation
	m.isThinking = true

	// Send to LLM
	return m, tea.Batch(
		func() tea.Msg { return ThinkingStartMsg{Message: "Processing..."} },
		m.sendToLLM(message),
	)
}

// sendToLLM sends a message to the LLM
func (m *MainModel) sendToLLM(message string) tea.Cmd {
	return func() tea.Msg {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Get recent messages for context
		messages := m.session.GetRecentMessages(10)

		// Get available tools
		tools := llm.GetAllTools()

		// Send to LLM with retry logic
		var response *types.Message
		var err error

		retryConfig := utils.LLMRetryConfig()
		err = utils.Retry(ctx, retryConfig, func() error {
			response, err = m.llmClient.Chat(ctx, messages, tools)
			return err
		})

		return LLMResponseMsg{
			Message: response,
			Error:   err,
		}
	}
}

// handleLLMResponse handles the LLM response
func (m *MainModel) handleLLMResponse(msg LLMResponseMsg) (tea.Model, tea.Cmd) {
	// Stop thinking animation
	m.isThinking = false
	m.thinking.Stop()

	if msg.Error != nil {
		// Handle error
		errorMsg := types.NewMessage(types.RoleAssistant, types.TypeError,
			fmt.Sprintf("Error: %v", msg.Error))
		m.session.AddMessage(errorMsg)
		m.history.AddMessage(errorMsg)
		m.lastError = msg.Error
		return m, nil
	}

	// Add assistant message to session
	m.session.AddMessage(msg.Message)
	m.history.AddMessage(msg.Message)

	// Handle tool calls if present
	if len(msg.Message.ToolCalls) > 0 {
		return m, m.executeToolCalls(msg.Message.ToolCalls)
	}

	return m, nil
}

// prepareExit prepares the exit information
func (m *MainModel) prepareExit() {
	duration := time.Since(m.started)

	stats := &SessionStats{
		MessageCount: len(m.session.Messages),
		CommandCount: len(m.session.CommandHistory),
		Duration:     duration,
		TokenUsage:   types.TokenUsage{
			TotalTokens: m.session.TokenUsage.TotalTokens,
		},
	}

	m.exitInfo = ExitInfo{
		Message:           "Goodbye! 👋",
		ShouldSaveSession: !m.options.NoHistory && len(m.session.Messages) > 0,
		HasErrors:         m.lastError != nil,
		SessionStats:      stats,
	}
}
