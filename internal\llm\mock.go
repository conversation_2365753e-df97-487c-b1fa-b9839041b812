package llm

import (
	"context"
	"fmt"
	"strings"
	"time"

	"arien-ai/pkg/types"
)

// MockClient implements the Client interface for testing and offline use
type MockClient struct {
	config types.LLMConfig
}

// NewMockClient creates a new mock client
func NewMockClient(config types.LLMConfig) (*MockClient, error) {
	return &MockClient{
		config: config,
	}, nil
}

// Chat sends a mock chat completion request
func (c *MockClient) Chat(ctx context.Context, messages []*types.Message, tools []Tool) (*types.Message, error) {
	if err := ValidateMessages(messages); err != nil {
		return nil, fmt.Errorf("invalid messages: %w", err)
	}

	// Get the last user message
	var lastUserMessage string
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == types.RoleUser {
			lastUserMessage = messages[i].Content
			break
		}
	}

	// Generate a mock response based on the input
	response := c.generateMockResponse(lastUserMessage, tools)
	
	// Create response message
	message := types.NewAssistantMessage(response)
	
	// Set mock token usage
	message.SetTokenUsage(&types.TokenUsage{
		PromptTokens:     len(lastUserMessage) / 4,  // Rough estimate
		CompletionTokens: len(response) / 4,         // Rough estimate
		TotalTokens:      (len(lastUserMessage) + len(response)) / 4,
	})

	return message, nil
}

// generateMockResponse generates a mock response based on the input
func (c *MockClient) generateMockResponse(input string, tools []Tool) string {
	input = strings.ToLower(strings.TrimSpace(input))
	
	// Handle common greetings
	if strings.Contains(input, "hello") || strings.Contains(input, "hi") || strings.Contains(input, "hey") {
		return "Hello! I'm a mock AI assistant running in test mode. I can help you test the CLI interface without requiring external API connections. Try asking me about commands, files, or system information!"
	}
	
	// Handle help requests
	if strings.Contains(input, "help") || strings.Contains(input, "what can you do") {
		return `I'm running in mock/test mode. Here's what I can help you test:

🔧 **System Commands**: Ask me to run commands like "list files" or "check system info"
📁 **File Operations**: Request file operations like "read file" or "write file"  
💬 **General Chat**: Have a conversation to test the chat interface
⚙️ **CLI Features**: Test slash commands like /status, /provider, /help

Since I'm a mock client, I won't actually execute real commands, but I can demonstrate the interface and tool calling functionality.

Try asking: "list the files in the current directory" or "what's the system status?"`
	}
	
	// Handle command requests
	if strings.Contains(input, "list") && (strings.Contains(input, "file") || strings.Contains(input, "directory")) {
		return `I'll help you list files. In a real scenario, I would use the execute_shell_command tool to run "ls" or "dir".

**Mock file listing:**
- README.md
- main.go
- internal/
- pkg/
- docs/
- arien-ai.exe

Would you like me to show you how tool calling works? Try asking for more specific operations!`
	}
	
	// Handle system info requests
	if strings.Contains(input, "system") && (strings.Contains(input, "info") || strings.Contains(input, "status")) {
		return `Here's mock system information:

🖥️ **System Status:**
- OS: Windows 11
- Architecture: x64
- CPU: Mock Intel i7 (8 cores)
- Memory: 16GB (60% used)
- Disk: 512GB SSD (45% used)

🔗 **Network:**
- Internet: Connected
- Local IP: *************

This is mock data for testing purposes. In real mode, I would gather actual system information using tools.`
	}
	
	// Handle file operations
	if strings.Contains(input, "read") && strings.Contains(input, "file") {
		return `I can help you read files! In real mode, I would use the read_file tool.

**Example mock file content:**
` + "```" + `
# Sample File
This is mock content for testing the file reading functionality.
The actual implementation would read real files from your system.
` + "```" + `

Try specifying a filename like "read the README.md file" for more specific responses!`
	}
	
	// Handle code/programming questions
	if strings.Contains(input, "code") || strings.Contains(input, "program") || strings.Contains(input, "function") {
		return `I can help with coding questions! Here's a mock response:

` + "```go" + `
// Example Go function
func mockExample() {
    fmt.Println("This is a mock code example")
    fmt.Println("In real mode, I could help with actual coding tasks")
}
` + "```" + `

I can assist with various programming languages and concepts. What specific coding help do you need?`
	}
	
	// Default response
	return fmt.Sprintf(`Thanks for your message: "%s"

I'm running in **mock mode** for testing the CLI interface. This means:

✅ The UI and chat interface are working
✅ Message handling is functional  
✅ Tool calling framework is ready
❌ No real API calls are made

To test real functionality:
- Use \`/provider deepseek\` (requires API key)
- Use \`/provider ollama\` (requires local Ollama)
- Use \`/status\` to check connectivity

Try different types of messages to test the interface!`, input)
}

// Stream sends a mock streaming chat completion request
func (c *MockClient) Stream(ctx context.Context, messages []*types.Message, tools []Tool, callback func(*types.Message)) error {
	if err := ValidateMessages(messages); err != nil {
		return fmt.Errorf("invalid messages: %w", err)
	}

	// Get the last user message
	var lastUserMessage string
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == types.RoleUser {
			lastUserMessage = messages[i].Content
			break
		}
	}

	// Generate response
	response := c.generateMockResponse(lastUserMessage, tools)
	
	// Simulate streaming by sending chunks
	message := types.NewAssistantMessage("")
	words := strings.Fields(response)
	
	for i, word := range words {
		if i > 0 {
			message.Content += " "
		}
		message.Content += word
		
		if callback != nil {
			callback(message)
		}
		
		// Small delay to simulate streaming
		time.Sleep(50 * time.Millisecond)
		
		// Check for context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
	}

	return nil
}

// GetModels returns mock models
func (c *MockClient) GetModels(ctx context.Context) ([]Model, error) {
	return []Model{
		{
			ID:          "mock-chat",
			Name:        "Mock Chat Model",
			Description: "Mock model for testing the CLI interface",
			Provider:    "mock",
			MaxTokens:   4096,
			Capabilities: []string{"chat", "test"},
		},
	}, nil
}

// ValidateConfig validates the mock client configuration
func (c *MockClient) ValidateConfig() error {
	// Mock client always validates successfully
	return nil
}

// GetProvider returns the provider name
func (c *MockClient) GetProvider() string {
	return "mock"
}

// Close closes the client and cleans up resources
func (c *MockClient) Close() error {
	// No cleanup needed for mock client
	return nil
}
