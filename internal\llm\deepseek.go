package llm

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/sashabaranov/go-openai"

	"arien-ai/pkg/types"
)

// DeepseekClient implements the Client interface for Deepseek API
type DeepseekClient struct {
	client *openai.Client
	config types.LLMConfig
}

// NewDeepseekClient creates a new Deepseek client
func NewDeepseekClient(config types.LLMConfig) (*DeepseekClient, error) {
	if config.APIKey == "" {
		return nil, fmt.Errorf("API key is required for Deepseek provider")
	}

	// Set default base URL if not provided
	baseURL := config.BaseURL
	if baseURL == "" {
		baseURL = "https://api.deepseek.com/v1"
	}

	// Create OpenAI client with Deepseek configuration and improved HTTP settings
	clientConfig := openai.DefaultConfig(config.APIKey)
	clientConfig.BaseURL = baseURL

	// Configure HTTP client with better settings for reliability
	clientConfig.HTTPClient = &http.Client{
		Timeout: 60 * time.Second, // Increased timeout
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
			TLSHandshakeTimeout: 10 * time.Second,
			DisableKeepAlives:  false,
		},
	}

	// Add custom headers if provided
	if len(config.Headers) > 0 {
		// Note: go-openai doesn't directly support custom headers in config
		// This would need to be implemented with a custom HTTP client
	}

	client := openai.NewClientWithConfig(clientConfig)

	return &DeepseekClient{
		client: client,
		config: config,
	}, nil
}

// Chat sends a chat completion request to Deepseek
func (c *DeepseekClient) Chat(ctx context.Context, messages []*types.Message, tools []Tool) (*types.Message, error) {
	if err := ValidateMessages(messages); err != nil {
		return nil, fmt.Errorf("invalid messages: %w", err)
	}

	if err := ValidateTools(tools); err != nil {
		return nil, fmt.Errorf("invalid tools: %w", err)
	}

	// Convert messages to OpenAI format
	openAIMessages := ConvertMessagesToOpenAI(messages)

	// Create chat completion request
	req := openai.ChatCompletionRequest{
		Model:       c.config.Model,
		Messages:    convertToOpenAIMessages(openAIMessages),
		Temperature: c.config.Temperature,
		MaxTokens:   c.config.MaxTokens,
	}

	// Add tools if provided
	if len(tools) > 0 {
		req.Tools = convertToOpenAITools(tools)
		req.ToolChoice = "auto"
	}

	// Send request
	resp, err := c.client.CreateChatCompletion(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no choices returned from API")
	}

	choice := resp.Choices[0]
	
	// Create response message
	message := types.NewAssistantMessage(choice.Message.Content)
	
	// Set token usage
	if resp.Usage.TotalTokens > 0 {
		message.SetTokenUsage(&types.TokenUsage{
			PromptTokens:     resp.Usage.PromptTokens,
			CompletionTokens: resp.Usage.CompletionTokens,
			TotalTokens:      resp.Usage.TotalTokens,
		})
	}

	// Handle tool calls
	if len(choice.Message.ToolCalls) > 0 {
		for _, tc := range choice.Message.ToolCalls {
			toolCall := types.ToolCall{
				ID:   tc.ID,
				Type: string(tc.Type),
				Function: types.FunctionCall{
					Name:      tc.Function.Name,
					Arguments: []byte(tc.Function.Arguments),
				},
			}
			message.AddToolCall(toolCall)
		}
	}

	return message, nil
}

// Stream sends a streaming chat completion request to Deepseek
func (c *DeepseekClient) Stream(ctx context.Context, messages []*types.Message, tools []Tool, callback func(*types.Message)) error {
	if err := ValidateMessages(messages); err != nil {
		return fmt.Errorf("invalid messages: %w", err)
	}

	if err := ValidateTools(tools); err != nil {
		return fmt.Errorf("invalid tools: %w", err)
	}

	// Convert messages to OpenAI format
	openAIMessages := ConvertMessagesToOpenAI(messages)

	// Create streaming chat completion request
	req := openai.ChatCompletionRequest{
		Model:       c.config.Model,
		Messages:    convertToOpenAIMessages(openAIMessages),
		Temperature: c.config.Temperature,
		MaxTokens:   c.config.MaxTokens,
		Stream:      true,
	}

	// Add tools if provided
	if len(tools) > 0 {
		req.Tools = convertToOpenAITools(tools)
		req.ToolChoice = "auto"
	}

	// Create stream
	stream, err := c.client.CreateChatCompletionStream(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to create chat completion stream: %w", err)
	}
	defer stream.Close()

	// Process stream
	var contentBuilder strings.Builder
	var currentMessage *types.Message

	for {
		response, err := stream.Recv()
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			return fmt.Errorf("error receiving stream: %w", err)
		}

		if len(response.Choices) == 0 {
			continue
		}

		choice := response.Choices[0]
		delta := choice.Delta

		// Initialize message if needed
		if currentMessage == nil {
			currentMessage = types.NewAssistantMessage("")
		}

		// Handle content delta
		if delta.Content != "" {
			contentBuilder.WriteString(delta.Content)
			currentMessage.Content = contentBuilder.String()
			
			// Call callback with updated message
			if callback != nil {
				callback(currentMessage)
			}
		}

		// Handle tool calls
		if len(delta.ToolCalls) > 0 {
			for _, tc := range delta.ToolCalls {
				toolCall := types.ToolCall{
					ID:   tc.ID,
					Type: string(tc.Type),
					Function: types.FunctionCall{
						Name:      tc.Function.Name,
						Arguments: []byte(tc.Function.Arguments),
					},
				}
				currentMessage.AddToolCall(toolCall)
			}
		}

		// Handle finish reason
		if choice.FinishReason != "" {
			break
		}
	}

	// Final callback with complete message
	if callback != nil && currentMessage != nil {
		callback(currentMessage)
	}

	return nil
}

// GetModels returns available models for Deepseek
func (c *DeepseekClient) GetModels(ctx context.Context) ([]Model, error) {
	// Deepseek models (hardcoded as the API might not provide a models endpoint)
	models := []Model{
		{
			ID:          "deepseek-chat",
			Name:        "DeepSeek Chat",
			Description: "DeepSeek's general-purpose chat model",
			Provider:    "deepseek",
			MaxTokens:   32768,
			Capabilities: []string{"chat", "function_calling"},
		},
		{
			ID:          "deepseek-reasoner",
			Name:        "DeepSeek Reasoner",
			Description: "DeepSeek's reasoning-focused model",
			Provider:    "deepseek",
			MaxTokens:   32768,
			Capabilities: []string{"chat", "reasoning", "function_calling"},
		},
	}

	return models, nil
}

// ValidateConfig validates the Deepseek client configuration
func (c *DeepseekClient) ValidateConfig() error {
	if c.config.APIKey == "" {
		return fmt.Errorf("API key is required for Deepseek provider")
	}

	if c.config.Model == "" {
		return fmt.Errorf("model is required")
	}

	// Validate model is supported
	supportedModels := []string{"deepseek-chat", "deepseek-reasoner"}
	modelSupported := false
	for _, model := range supportedModels {
		if c.config.Model == model {
			modelSupported = true
			break
		}
	}

	if !modelSupported {
		return fmt.Errorf("unsupported model: %s", c.config.Model)
	}

	return nil
}

// GetProvider returns the provider name
func (c *DeepseekClient) GetProvider() string {
	return "deepseek"
}

// Close closes the client and cleans up resources
func (c *DeepseekClient) Close() error {
	// No cleanup needed for HTTP client
	return nil
}

// Helper functions to convert between formats

func convertToOpenAIMessages(messages []map[string]interface{}) []openai.ChatCompletionMessage {
	var openAIMessages []openai.ChatCompletionMessage
	
	for _, msg := range messages {
		openAIMsg := openai.ChatCompletionMessage{
			Role:    msg["role"].(string),
			Content: msg["content"].(string),
		}
		
		// Handle tool calls if present
		if toolCalls, ok := msg["tool_calls"]; ok {
			if tcSlice, ok := toolCalls.([]map[string]interface{}); ok {
				var openAIToolCalls []openai.ToolCall
				for _, tc := range tcSlice {
					openAIToolCall := openai.ToolCall{
						ID:   tc["id"].(string),
						Type: openai.ToolType(tc["type"].(string)),
					}
					
					if function, ok := tc["function"].(map[string]interface{}); ok {
						openAIToolCall.Function = openai.FunctionCall{
							Name:      function["name"].(string),
							Arguments: function["arguments"].(string),
						}
					}
					
					openAIToolCalls = append(openAIToolCalls, openAIToolCall)
				}
				openAIMsg.ToolCalls = openAIToolCalls
			}
		}
		
		// Handle tool call ID for tool messages
		if toolCallID, ok := msg["tool_call_id"]; ok {
			openAIMsg.ToolCallID = toolCallID.(string)
		}
		
		openAIMessages = append(openAIMessages, openAIMsg)
	}
	
	return openAIMessages
}

func convertToOpenAITools(tools []Tool) []openai.Tool {
	var openAITools []openai.Tool
	
	for _, tool := range tools {
		openAITool := openai.Tool{
			Type: openai.ToolType(tool.Type),
			Function: &openai.FunctionDefinition{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				Parameters:  tool.Function.Parameters,
			},
		}
		openAITools = append(openAITools, openAITool)
	}
	
	return openAITools
}
