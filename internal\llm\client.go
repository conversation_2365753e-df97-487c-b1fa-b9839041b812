package llm

import (
	"context"
	"fmt"

	"arien-ai/pkg/types"
)

// Client interface defines the contract for LLM providers
type Client interface {
	// Cha<PERSON> sends a chat completion request
	Chat(ctx context.Context, messages []*types.Message, tools []Tool) (*types.Message, error)
	
	// Stream sends a streaming chat completion request
	Stream(ctx context.Context, messages []*types.Message, tools []Tool, callback func(*types.Message)) error
	
	// GetModels returns available models for the provider
	GetModels(ctx context.Context) ([]Model, error)
	
	// ValidateConfig validates the client configuration
	ValidateConfig() error
	
	// GetProvider returns the provider name
	GetProvider() string
	
	// Close closes the client and cleans up resources
	Close() error
}

// Model represents an available LLM model
type Model struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	Provider    string `json:"provider"`
	MaxTokens   int    `json:"max_tokens,omitempty"`
	Capabilities []string `json:"capabilities,omitempty"`
}

// Tool represents a function that can be called by the LLM
type Tool struct {
	Type     string   `json:"type"`
	Function Function `json:"function"`
}

// Function represents a function definition for tool calling
type Function struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Parameters  interface{} `json:"parameters"`
}

// ChatRequest represents a chat completion request
type ChatRequest struct {
	Messages    []*types.Message `json:"messages"`
	Tools       []Tool           `json:"tools,omitempty"`
	Model       string           `json:"model"`
	Temperature float32          `json:"temperature,omitempty"`
	MaxTokens   int              `json:"max_tokens,omitempty"`
	Stream      bool             `json:"stream,omitempty"`
}

// ChatResponse represents a chat completion response
type ChatResponse struct {
	Message    *types.Message     `json:"message"`
	TokenUsage *types.TokenUsage  `json:"token_usage,omitempty"`
	Model      string             `json:"model"`
	Provider   string             `json:"provider"`
}

// NewClient creates a new LLM client based on the configuration
func NewClient(config types.LLMConfig) (Client, error) {
	switch config.Provider {
	case "deepseek":
		return NewDeepseekClient(config)
	case "ollama":
		return NewOllamaClient(config)
	case "mock", "test":
		return NewMockClient(config)
	default:
		return nil, fmt.Errorf("unsupported LLM provider: %s (available: deepseek, ollama, mock)", config.Provider)
	}
}

// ConvertMessagesToOpenAI converts internal messages to OpenAI format
func ConvertMessagesToOpenAI(messages []*types.Message) []map[string]interface{} {
	var openAIMessages []map[string]interface{}
	
	for _, msg := range messages {
		openAIMsg := map[string]interface{}{
			"role":    string(msg.Role),
			"content": msg.Content,
		}
		
		// Add tool calls if present
		if len(msg.ToolCalls) > 0 {
			var toolCalls []map[string]interface{}
			for _, tc := range msg.ToolCalls {
				toolCall := map[string]interface{}{
					"id":   tc.ID,
					"type": tc.Type,
					"function": map[string]interface{}{
						"name":      tc.Function.Name,
						"arguments": string(tc.Function.Arguments),
					},
				}
				toolCalls = append(toolCalls, toolCall)
			}
			openAIMsg["tool_calls"] = toolCalls
		}
		
		// Add tool call ID for tool messages
		if msg.Role == types.RoleTool && msg.ToolResult != nil {
			openAIMsg["tool_call_id"] = msg.ToolResult.ToolCallID
		}
		
		openAIMessages = append(openAIMessages, openAIMsg)
	}
	
	return openAIMessages
}

// ConvertToolsToOpenAI converts internal tools to OpenAI format
func ConvertToolsToOpenAI(tools []Tool) []map[string]interface{} {
	var openAITools []map[string]interface{}
	
	for _, tool := range tools {
		openAITool := map[string]interface{}{
			"type": tool.Type,
			"function": map[string]interface{}{
				"name":        tool.Function.Name,
				"description": tool.Function.Description,
				"parameters":  tool.Function.Parameters,
			},
		}
		openAITools = append(openAITools, openAITool)
	}
	
	return openAITools
}

// ValidateMessages validates a slice of messages
func ValidateMessages(messages []*types.Message) error {
	if len(messages) == 0 {
		return fmt.Errorf("messages cannot be empty")
	}
	
	for i, msg := range messages {
		if msg == nil {
			return fmt.Errorf("message at index %d is nil", i)
		}
		
		if msg.Role == "" {
			return fmt.Errorf("message at index %d has empty role", i)
		}
		
		if msg.Content == "" && len(msg.ToolCalls) == 0 {
			return fmt.Errorf("message at index %d has empty content and no tool calls", i)
		}
	}
	
	return nil
}

// ValidateTools validates a slice of tools
func ValidateTools(tools []Tool) error {
	for i, tool := range tools {
		if tool.Type == "" {
			return fmt.Errorf("tool at index %d has empty type", i)
		}
		
		if tool.Function.Name == "" {
			return fmt.Errorf("tool at index %d has empty function name", i)
		}
		
		if tool.Function.Description == "" {
			return fmt.Errorf("tool at index %d has empty function description", i)
		}
	}
	
	return nil
}

// GetAvailableProviders returns a list of available LLM providers
func GetAvailableProviders() []string {
	return []string{"deepseek", "ollama"}
}

// GetDefaultModels returns default models for each provider
func GetDefaultModels() map[string][]string {
	return map[string][]string{
		"deepseek": {"deepseek-chat", "deepseek-reasoner"},
		"ollama":   {"llama3.3", "deepseek-r1", "qwen2.5"},
	}
}

// IsProviderSupported checks if a provider is supported
func IsProviderSupported(provider string) bool {
	for _, p := range GetAvailableProviders() {
		if p == provider {
			return true
		}
	}
	return false
}
