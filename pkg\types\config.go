package types

import "time"

// Config represents the application configuration
type Config struct {
	LLM      LLMConfig      `yaml:"llm" json:"llm"`
	UI       UIConfig       `yaml:"ui" json:"ui"`
	Session  SessionConfig  `yaml:"session" json:"session"`
	Security SecurityConfig `yaml:"security" json:"security"`
}

// LLMConfig contains LLM provider configurations
type LLMConfig struct {
	Provider    string            `yaml:"provider" json:"provider"`       // "deepseek" or "ollama"
	Model       string            `yaml:"model" json:"model"`             // Model name
	APIKey      string            `yaml:"api_key" json:"api_key"`         // API key for external providers
	BaseURL     string            `yaml:"base_url" json:"base_url"`       // Base URL for API
	Temperature float32           `yaml:"temperature" json:"temperature"` // Model temperature
	MaxTokens   int               `yaml:"max_tokens" json:"max_tokens"`   // Maximum tokens
	Timeout     time.Duration     `yaml:"timeout" json:"timeout"`         // Request timeout
	Headers     map[string]string `yaml:"headers" json:"headers"`         // Additional headers
}

// UIConfig contains UI/UX preferences
type UIConfig struct {
	Theme           string `yaml:"theme" json:"theme"`                       // UI theme
	ShowTimestamps  bool   `yaml:"show_timestamps" json:"show_timestamps"`   // Show message timestamps
	ShowTokenCount  bool   `yaml:"show_token_count" json:"show_token_count"` // Show token usage
	AnimationSpeed  int    `yaml:"animation_speed" json:"animation_speed"`   // Animation speed (ms)
	MaxHistorySize  int    `yaml:"max_history_size" json:"max_history_size"` // Max messages in history
	AutoScroll      bool   `yaml:"auto_scroll" json:"auto_scroll"`           // Auto-scroll to bottom
	ConfirmCommands bool   `yaml:"confirm_commands" json:"confirm_commands"` // Confirm before executing commands
}

// SessionConfig contains session management settings
type SessionConfig struct {
	AutoSave         bool          `yaml:"auto_save" json:"auto_save"`                   // Auto-save sessions
	SaveInterval     time.Duration `yaml:"save_interval" json:"save_interval"`           // Save interval
	MaxSessions      int           `yaml:"max_sessions" json:"max_sessions"`             // Maximum stored sessions
	SessionDirectory string        `yaml:"session_directory" json:"session_directory"`   // Directory for session files
	ContextWindow    int           `yaml:"context_window" json:"context_window"`         // Context window size
}

// SecurityConfig contains security settings
type SecurityConfig struct {
	AllowedCommands    []string      `yaml:"allowed_commands" json:"allowed_commands"`       // Whitelist of commands
	BlockedCommands    []string      `yaml:"blocked_commands" json:"blocked_commands"`       // Blacklist of commands
	RequireConfirm     []string      `yaml:"require_confirm" json:"require_confirm"`         // Commands requiring confirmation
	MaxCommandTimeout  time.Duration `yaml:"max_command_timeout" json:"max_command_timeout"` // Maximum command execution time
	SandboxMode        bool          `yaml:"sandbox_mode" json:"sandbox_mode"`               // Enable sandbox mode
	AllowNetworkAccess bool          `yaml:"allow_network_access" json:"allow_network_access"` // Allow network commands
}

// DefaultConfig returns a default configuration
func DefaultConfig() *Config {
	return &Config{
		LLM: LLMConfig{
			Provider:    "mock", // Start with mock for better first-time experience
			Model:       "mock-chat",
			Temperature: 0.7,
			MaxTokens:   4096,
			Timeout:     30 * time.Second,
			Headers:     make(map[string]string),
		},
		UI: UIConfig{
			Theme:           "default",
			ShowTimestamps:  true,
			ShowTokenCount:  true,
			AnimationSpeed:  100,
			MaxHistorySize:  1000,
			AutoScroll:      true,
			ConfirmCommands: true,
		},
		Session: SessionConfig{
			AutoSave:         true,
			SaveInterval:     5 * time.Minute,
			MaxSessions:      50,
			SessionDirectory: "~/.arien-ai/sessions",
			ContextWindow:    10,
		},
		Security: SecurityConfig{
			AllowedCommands: []string{
				"ls", "dir", "pwd", "cd", "cat", "head", "tail", "grep", "find",
				"git", "npm", "go", "python", "pip", "docker", "kubectl",
			},
			BlockedCommands: []string{
				"rm -rf /", "del /f /s /q", "format", "fdisk", "dd",
				"shutdown", "reboot", "halt", "poweroff",
			},
			RequireConfirm: []string{
				"rm", "del", "mv", "cp", "chmod", "chown", "sudo",
				"docker run", "docker exec", "kubectl delete",
			},
			MaxCommandTimeout:  5 * time.Minute,
			SandboxMode:        false,
			AllowNetworkAccess: true,
		},
	}
}
