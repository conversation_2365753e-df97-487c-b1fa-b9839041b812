package models

import (
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"

	"arien-ai/internal/llm"
	"arien-ai/internal/ui/components"
	"arien-ai/pkg/types"
)

// createSlashCommands creates the available slash commands
func createSlashCommands() []SlashCommand {
	return []SlashCommand{
		{
			Name:        "help",
			Description: "Show available commands",
			Handler:     handleHelpCommand,
		},
		{
			Name:        "retry",
			Description: "Retry the last message",
			Handler:     handleRetryCommand,
		},
		{
			Name:        "clear",
			Description: "Clear conversation history",
			Handler:     handleClearCommand,
		},
		{
			Name:        "save",
			Description: "Save current session",
			Handler:     handleSaveCommand,
		},
		{
			Name:        "load",
			Description: "Load a session",
			Handler:     handleLoadCommand,
		},
		{
			Name:        "sessions",
			Description: "List all sessions",
			Handler:     handleSessionsCommand,
		},
		{
			Name:        "model",
			Description: "Change LLM model",
			Handler:     handleModelCommand,
		},
		{
			Name:        "provider",
			Description: "Change LLM provider",
			Handler:     handleProviderCommand,
		},
		{
			Name:        "config",
			Description: "Show configuration",
			Handler:     handleConfigCommand,
		},
		{
			Name:        "theme",
			Description: "Change UI theme",
			Handler:     handleThemeCommand,
		},
		{
			Name:        "export",
			Description: "Export conversation",
			Handler:     handleExportCommand,
		},
		{
			Name:        "stats",
			Description: "Show session statistics",
			Handler:     handleStatsCommand,
		},
		{
			Name:        "status",
			Description: "Show system status",
			Handler:     handleStatusCommand,
		},
		{
			Name:        "quit",
			Description: "Exit the application",
			Handler:     handleQuitCommand,
		},
	}
}

// handleSlashCommand processes a slash command
func (m *MainModel) handleSlashCommand(command string) (tea.Model, tea.Cmd) {
	// Clear input and reset mode
	m.input.Clear()
	m.showSlashMenu = false
	m.currentMode = ModeNormal
	m.input.SetMode(components.ModeNormal)
	
	// Parse command and arguments
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return m, nil
	}
	
	commandName := strings.TrimPrefix(parts[0], "/")
	args := parts[1:]
	
	// Find and execute command
	for _, cmd := range m.slashCommands {
		if cmd.Name == commandName {
			return m, cmd.Handler(m, args)
		}
	}
	
	// Unknown command
	errorMsg := types.NewMessage(types.RoleSystem, types.TypeError, 
		fmt.Sprintf("Unknown command: /%s", commandName))
	m.session.AddMessage(errorMsg)
	m.history.AddMessage(errorMsg)
	
	return m, nil
}

// handleSlashNavigation handles navigation in the slash menu
func (m *MainModel) handleSlashNavigation(direction string) (tea.Model, tea.Cmd) {
	switch direction {
	case "up":
		if m.selectedSlash > 0 {
			m.selectedSlash--
		}
	case "down":
		if m.selectedSlash < len(m.slashCommands)-1 {
			m.selectedSlash++
		}
	}
	return m, nil
}

// renderSlashMenu renders the slash command menu
func (m *MainModel) renderSlashMenu() string {
	if !m.showSlashMenu || len(m.slashCommands) == 0 {
		return ""
	}
	
	var items []string
	for i, cmd := range m.slashCommands {
		style := m.theme.Styles.SlashMenuItem
		if i == m.selectedSlash {
			style = m.theme.Styles.SlashMenuActive
		}
		
		item := fmt.Sprintf("/%s - %s", cmd.Name, cmd.Description)
		items = append(items, style.Render(item))
	}
	
	content := strings.Join(items, "\n")
	return m.theme.Styles.SlashMenu.Render(content)
}

// Command handlers

func handleHelpCommand(m *MainModel, args []string) tea.Cmd {
	var helpText strings.Builder
	helpText.WriteString("Available slash commands:\n\n")
	
	for _, cmd := range m.slashCommands {
		helpText.WriteString(fmt.Sprintf("/%s - %s\n", cmd.Name, cmd.Description))
	}
	
	helpText.WriteString("\nKeyboard shortcuts:\n")
	helpText.WriteString("Ctrl+C - Exit\n")
	helpText.WriteString("/ - Open command menu\n")
	helpText.WriteString("Esc - Close command menu\n")
	helpText.WriteString("↑/↓ - Navigate command menu\n")
	
	helpMsg := types.NewSystemMessage(helpText.String())
	m.session.AddMessage(helpMsg)
	m.history.AddMessage(helpMsg)
	
	return nil
}

func handleClearCommand(m *MainModel, args []string) tea.Cmd {
	m.history.Clear()
	m.session.Messages = make([]*types.Message, 0)
	
	clearMsg := types.NewSystemMessage("Conversation history cleared.")
	m.session.AddMessage(clearMsg)
	m.history.AddMessage(clearMsg)
	
	return nil
}

func handleSaveCommand(m *MainModel, args []string) tea.Cmd {
	err := m.SaveCurrentSession()
	
	var message string
	if err != nil {
		message = fmt.Sprintf("Failed to save session: %v", err)
	} else {
		message = fmt.Sprintf("Session saved successfully: %s", m.session.ID)
	}
	
	saveMsg := types.NewSystemMessage(message)
	m.session.AddMessage(saveMsg)
	m.history.AddMessage(saveMsg)
	
	return nil
}

func handleLoadCommand(m *MainModel, args []string) tea.Cmd {
	if len(args) == 0 {
		errorMsg := types.NewSystemMessage("Usage: /load <session-id>")
		m.session.AddMessage(errorMsg)
		m.history.AddMessage(errorMsg)
		return nil
	}
	
	sessionID := args[0]
	session, err := m.sessionManager.LoadSession(sessionID)
	
	var message string
	if err != nil {
		message = fmt.Sprintf("Failed to load session: %v", err)
	} else {
		// Switch to loaded session
		m.session = session
		m.history.SetMessages(session.Messages)
		m.header.Update(session)
		message = fmt.Sprintf("Session loaded successfully: %s", sessionID)
	}
	
	loadMsg := types.NewSystemMessage(message)
	m.session.AddMessage(loadMsg)
	m.history.AddMessage(loadMsg)
	
	return nil
}

func handleSessionsCommand(m *MainModel, args []string) tea.Cmd {
	summaries, err := m.sessionManager.ListSessions()
	
	var message strings.Builder
	if err != nil {
		message.WriteString(fmt.Sprintf("Failed to list sessions: %v", err))
	} else {
		message.WriteString("Available sessions:\n\n")
		
		if len(summaries) == 0 {
			message.WriteString("No sessions found.")
		} else {
			for _, summary := range summaries {
				message.WriteString(fmt.Sprintf("ID: %s\n", summary.ID))
				message.WriteString(fmt.Sprintf("Name: %s\n", summary.Name))
				message.WriteString(fmt.Sprintf("Messages: %d\n", summary.MessageCount))
				message.WriteString(fmt.Sprintf("Last Activity: %s\n", 
					summary.LastActivity.Format("2006-01-02 15:04:05")))
				message.WriteString("\n")
			}
		}
	}
	
	sessionsMsg := types.NewSystemMessage(message.String())
	m.session.AddMessage(sessionsMsg)
	m.history.AddMessage(sessionsMsg)
	
	return nil
}

func handleModelCommand(m *MainModel, args []string) tea.Cmd {
	if len(args) == 0 {
		// Show current model
		message := fmt.Sprintf("Current model: %s", m.configManager.Get().LLM.Model)
		modelMsg := types.NewSystemMessage(message)
		m.session.AddMessage(modelMsg)
		m.history.AddMessage(modelMsg)
		return nil
	}
	
	// Change model
	newModel := args[0]
	config := m.configManager.Get()
	config.LLM.Model = newModel
	
	err := m.configManager.UpdateLLMConfig(config.LLM)
	
	var message string
	if err != nil {
		message = fmt.Sprintf("Failed to change model: %v", err)
	} else {
		message = fmt.Sprintf("Model changed to: %s", newModel)
		// TODO: Recreate LLM client with new model
	}
	
	modelMsg := types.NewSystemMessage(message)
	m.session.AddMessage(modelMsg)
	m.history.AddMessage(modelMsg)
	
	return nil
}

func handleProviderCommand(m *MainModel, args []string) tea.Cmd {
	if len(args) == 0 {
		// Show current provider
		message := fmt.Sprintf("Current provider: %s", m.configManager.Get().LLM.Provider)
		providerMsg := types.NewSystemMessage(message)
		m.session.AddMessage(providerMsg)
		m.history.AddMessage(providerMsg)
		return nil
	}

	// Change provider
	newProvider := args[0]
	config := m.configManager.Get()
	oldProvider := config.LLM.Provider

	// Set provider-specific defaults
	switch newProvider {
	case "deepseek":
		config.LLM.Provider = "deepseek"
		if config.LLM.Model == "" || strings.Contains(config.LLM.Model, "llama") || strings.Contains(config.LLM.Model, "mock") {
			config.LLM.Model = "deepseek-chat"
		}
		config.LLM.BaseURL = "https://api.deepseek.com/v1"
	case "ollama":
		config.LLM.Provider = "ollama"
		if config.LLM.Model == "" || strings.Contains(config.LLM.Model, "deepseek") || strings.Contains(config.LLM.Model, "mock") {
			config.LLM.Model = "llama3.3"
		}
		config.LLM.BaseURL = "http://localhost:11434/v1"
	case "mock", "test":
		config.LLM.Provider = "mock"
		config.LLM.Model = "mock-chat"
		config.LLM.BaseURL = ""
		config.LLM.APIKey = "" // No API key needed for mock
	default:
		message := fmt.Sprintf("Unknown provider: %s. Available: deepseek, ollama, mock", newProvider)
		errorMsg := types.NewSystemMessage(message)
		m.session.AddMessage(errorMsg)
		m.history.AddMessage(errorMsg)
		return nil
	}

	// Save configuration
	err := m.configManager.UpdateLLMConfig(config.LLM)

	var message string
	if err != nil {
		message = fmt.Sprintf("Failed to change provider: %v", err)
	} else {
		// Try to create new LLM client
		newClient, clientErr := llm.NewClient(config.LLM)
		if clientErr != nil {
			// Revert to old provider
			config.LLM.Provider = oldProvider
			m.configManager.UpdateLLMConfig(config.LLM)
			message = fmt.Sprintf("Failed to create %s client: %v", newProvider, clientErr)
		} else {
			// Close old client and use new one
			if m.llmClient != nil {
				m.llmClient.Close()
			}
			m.llmClient = newClient

			// Update header and status bar
			m.header.Update(m.session)
			m.statusBar.Update(m.session, config)

			message = fmt.Sprintf("✅ Provider changed to: %s (model: %s)", newProvider, config.LLM.Model)
		}
	}
	
	providerMsg := types.NewSystemMessage(message)
	m.session.AddMessage(providerMsg)
	m.history.AddMessage(providerMsg)
	
	return nil
}

func handleConfigCommand(m *MainModel, args []string) tea.Cmd {
	config := m.configManager.Get()
	
	var message strings.Builder
	message.WriteString("Current Configuration:\n\n")
	message.WriteString(fmt.Sprintf("LLM Provider: %s\n", config.LLM.Provider))
	message.WriteString(fmt.Sprintf("Model: %s\n", config.LLM.Model))
	message.WriteString(fmt.Sprintf("Base URL: %s\n", config.LLM.BaseURL))
	message.WriteString(fmt.Sprintf("Temperature: %.2f\n", config.LLM.Temperature))
	message.WriteString(fmt.Sprintf("Max Tokens: %d\n", config.LLM.MaxTokens))
	message.WriteString(fmt.Sprintf("Theme: %s\n", config.UI.Theme))
	message.WriteString(fmt.Sprintf("Sandbox Mode: %t\n", config.Security.SandboxMode))
	
	configMsg := types.NewSystemMessage(message.String())
	m.session.AddMessage(configMsg)
	m.history.AddMessage(configMsg)
	
	return nil
}

func handleThemeCommand(m *MainModel, args []string) tea.Cmd {
	if len(args) == 0 {
		// Show available themes
		themes := []string{"default", "dark", "light"}
		message := fmt.Sprintf("Available themes: %s\nCurrent theme: %s", 
			strings.Join(themes, ", "), m.configManager.Get().UI.Theme)
		themeMsg := types.NewSystemMessage(message)
		m.session.AddMessage(themeMsg)
		m.history.AddMessage(themeMsg)
		return nil
	}
	
	// Change theme
	newTheme := args[0]
	config := m.configManager.Get()
	config.UI.Theme = newTheme
	
	err := m.configManager.UpdateUIConfig(config.UI)
	
	var message string
	if err != nil {
		message = fmt.Sprintf("Failed to change theme: %v", err)
	} else {
		message = fmt.Sprintf("Theme changed to: %s", newTheme)
		// TODO: Update theme in UI components
	}
	
	themeMsg := types.NewSystemMessage(message)
	m.session.AddMessage(themeMsg)
	m.history.AddMessage(themeMsg)
	
	return nil
}

func handleExportCommand(m *MainModel, args []string) tea.Cmd {
	format := "plain"
	if len(args) > 0 {
		format = args[0]
	}
	
	exported := m.history.ExportMessages(format)
	
	// For now, just show the export in a message
	// In a full implementation, you might save to file
	exportMsg := types.NewSystemMessage(fmt.Sprintf("Exported conversation (%s format):\n\n%s", format, exported))
	m.session.AddMessage(exportMsg)
	m.history.AddMessage(exportMsg)
	
	return nil
}

func handleStatsCommand(m *MainModel, args []string) tea.Cmd {
	var message strings.Builder
	message.WriteString("Session Statistics:\n\n")
	message.WriteString(fmt.Sprintf("Session ID: %s\n", m.session.ID))
	message.WriteString(fmt.Sprintf("Messages: %d\n", len(m.session.Messages)))
	message.WriteString(fmt.Sprintf("Commands Executed: %d\n", len(m.session.CommandHistory)))
	message.WriteString(fmt.Sprintf("Total Tokens: %d\n", m.session.TokenUsage.TotalTokens))
	message.WriteString(fmt.Sprintf("Requests: %d\n", m.session.TokenUsage.RequestCount))
	message.WriteString(fmt.Sprintf("Created: %s\n", m.session.CreatedAt.Format("2006-01-02 15:04:05")))
	message.WriteString(fmt.Sprintf("Last Activity: %s\n", m.session.LastActivity.Format("2006-01-02 15:04:05")))
	
	duration := time.Since(m.started)
	message.WriteString(fmt.Sprintf("Session Duration: %v\n", duration))
	
	statsMsg := types.NewSystemMessage(message.String())
	m.session.AddMessage(statsMsg)
	m.history.AddMessage(statsMsg)
	
	return nil
}

func handleRetryCommand(m *MainModel, args []string) tea.Cmd {
	// Find the last user message
	var lastUserMessage *types.Message
	for i := len(m.session.Messages) - 1; i >= 0; i-- {
		if m.session.Messages[i].Role == types.RoleUser {
			lastUserMessage = m.session.Messages[i]
			break
		}
	}

	if lastUserMessage == nil {
		retryMsg := types.NewSystemMessage("No previous message to retry.")
		m.session.AddMessage(retryMsg)
		m.history.AddMessage(retryMsg)
		return nil
	}

	// Retry the last message
	retryMsg := types.NewSystemMessage("🔄 Retrying last message...")
	m.session.AddMessage(retryMsg)
	m.history.AddMessage(retryMsg)

	// Start thinking animation and send to LLM
	m.isThinking = true
	return tea.Batch(
		func() tea.Msg { return ThinkingStartMsg{Message: "Retrying..."} },
		m.sendToLLM(lastUserMessage.Content),
	)
}

func handleStatusCommand(m *MainModel, args []string) tea.Cmd {
	var status strings.Builder
	config := m.configManager.Get()

	status.WriteString("🔧 System Status\n\n")
	status.WriteString(fmt.Sprintf("Provider: %s\n", config.LLM.Provider))
	status.WriteString(fmt.Sprintf("Model: %s\n", config.LLM.Model))
	status.WriteString(fmt.Sprintf("Base URL: %s\n", config.LLM.BaseURL))
	status.WriteString(fmt.Sprintf("Session ID: %s\n", m.session.ID))
	status.WriteString(fmt.Sprintf("Messages: %d\n", len(m.session.Messages)))

	// Check provider connectivity
	status.WriteString("\n🌐 Connectivity:\n")

	// Test Deepseek
	if config.LLM.Provider == "deepseek" || config.LLM.APIKey != "" {
		status.WriteString("• Deepseek: ")
		deepseekConfig := config.LLM
		deepseekConfig.Provider = "deepseek"
		if deepseekClient, err := llm.NewClient(deepseekConfig); err == nil {
			if err := deepseekClient.ValidateConfig(); err == nil {
				status.WriteString("✅ Available\n")
			} else {
				status.WriteString(fmt.Sprintf("❌ Error: %v\n", err))
			}
			deepseekClient.Close()
		} else {
			status.WriteString(fmt.Sprintf("❌ Error: %v\n", err))
		}
	}

	// Test Ollama
	status.WriteString("• Ollama: ")
	ollamaConfig := config.LLM
	ollamaConfig.Provider = "ollama"
	ollamaConfig.Model = "llama3.3"
	ollamaConfig.BaseURL = "http://localhost:11434/v1"
	if ollamaClient, err := llm.NewClient(ollamaConfig); err == nil {
		if err := ollamaClient.ValidateConfig(); err == nil {
			status.WriteString("✅ Available\n")
		} else {
			status.WriteString("❌ Not running\n")
		}
		ollamaClient.Close()
	} else {
		status.WriteString("❌ Not available\n")
	}

	// Test Mock
	status.WriteString("• Mock: ")
	mockConfig := config.LLM
	mockConfig.Provider = "mock"
	mockConfig.Model = "mock-chat"
	if mockClient, err := llm.NewClient(mockConfig); err == nil {
		status.WriteString("✅ Available (for testing)\n")
		mockClient.Close()
	} else {
		status.WriteString("❌ Error\n")
	}

	statusMsg := types.NewSystemMessage(status.String())
	m.session.AddMessage(statusMsg)
	m.history.AddMessage(statusMsg)

	return nil
}

func handleQuitCommand(m *MainModel, args []string) tea.Cmd {
	m.prepareExit()
	return tea.Quit
}
